'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { useBooking } from '@/contexts/BookingContext';
import { Ticket, Printer, Plus, Calendar, MapPin, User, CreditCard, Bus, Home as HomeIcon } from 'lucide-react';
import { motion } from 'framer-motion';
import { format } from 'date-fns';

export default function TicketPage() {
  const [travelDates, setTravelDates] = useState({ goDate: '', returnDate: '' });
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { bookingData, resetBookingData } = useBooking();

  useEffect(() => {
    // Check if booking data is available
    if (!bookingData.studentName || !bookingData.admissionNumber) {
      console.log('Missing student data in booking context, redirecting to details page');
      router.push('/details');
      return;
    }

    if (!bookingData.busRoute || !bookingData.destination) {
      console.log('Missing booking data, redirecting to buses page');
      router.push('/buses');
      return;
    }

    fetchTravelDates();
  }, [bookingData, router]);

  const fetchTravelDates = async () => {
    try {
      const [goResponse, returnResponse] = await Promise.all([
        fetch('/api/travel-dates/go'),
        fetch('/api/travel-dates/return'),
      ]);

      const goData = await goResponse.json();
      const returnData = await returnResponse.json();

      setTravelDates({
        goDate: goData.date || 'Not set',
        returnDate: returnData.date || 'Not set',
      });
    } catch (error) {
      console.error('Failed to fetch travel dates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleBookAnother = () => {
    resetBookingData();
    router.push('/');
  };

  // Show loading state while checking data or fetching travel dates
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Generating ticket...</p>
        </div>
      </div>
    );
  }

  // If no booking data, don't render anything (will redirect)
  if (!bookingData.studentName || !bookingData.admissionNumber || !bookingData.busRoute || !bookingData.destination) {
    return null;
  }

  return (
    <PageTransition direction="right">
      <div className="min-h-screen p-4 bg-gradient-to-br from-orange-50 via-white to-red-50 print:min-h-0 print:p-3">
        <div className="max-w-4xl mx-auto print:max-w-none print:mx-2">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8 print:hidden"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                <Ticket className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-gray-800">Booking Confirmed!</h1>
            </div>
            <p className="text-gray-600 text-lg">Your bus pass has been successfully booked</p>
          </motion.div>

          {/* Ticket */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8 print:mb-2"
          >
            <Card className="shadow-2xl border-2 border-orange-300 bg-white print:shadow-md print:border-2 print:border-orange-400">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg print:bg-gradient-to-r print:from-orange-600 print:to-red-600 print:py-4">
                <CardTitle className="text-center">
                  <div className="flex items-center justify-center gap-3 mb-2">
                    <Ticket className="w-8 h-8 print:w-7 print:h-7" />
                    <span className="text-2xl font-bold print:text-xl">ST. JOSEPH'S COLLEGE OF ENGINEERING AND TECHNOLOGY, PALAI</span>
                  </div>
                  <div className="text-lg print:text-base">Bus Pass Ticket</div>
                  <div className="text-sm opacity-90 print:text-sm print:opacity-100">Student Generated</div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8 print:p-5">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 print:gap-5">
                  {/* Left Column - All Information */}
                  <div className="lg:col-span-2 space-y-6 print:space-y-4">
                    {/* Student Information */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 print:bg-blue-50 print:p-3 print:border-blue-200">
                      <h3 className="font-semibold text-blue-800 mb-3 flex items-center gap-2 print:text-blue-800 print:mb-2 print:text-sm">
                        <User className="w-5 h-5 print:w-5 print:h-5" />
                        Student Information
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Name:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{bookingData.studentName}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Admission No:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{bookingData.admissionNumber}</span>
                        </div>
                      </div>
                    </div>

                    {/* Travel Dates */}
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 print:bg-green-50 print:p-3 print:border-green-200">
                      <h3 className="font-semibold text-green-800 mb-3 flex items-center gap-2 print:text-green-800 print:mb-2 print:text-sm">
                        <Calendar className="w-5 h-5 print:w-5 print:h-5" />
                        Travel Dates
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Go Date:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{travelDates.goDate}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Return Date:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{travelDates.returnDate}</span>
                        </div>
                      </div>
                    </div>

                    {/* Bus Information */}
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 print:bg-purple-50 print:p-3 print:border-purple-200">
                      <h3 className="font-semibold text-purple-800 mb-3 flex items-center gap-2 print:text-purple-800 print:mb-2 print:text-sm">
                        <Bus className="w-5 h-5 print:w-5 print:h-5" />
                        Bus Information
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Bus Name:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{bookingData.busName}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Route:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{bookingData.busRoute}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Destination:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{bookingData.destination}</span>
                        </div>
                      </div>
                    </div>

                    {/* Payment Information */}
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 print:bg-orange-50 print:p-3 print:border-orange-200">
                      <h3 className="font-semibold text-orange-800 mb-3 flex items-center gap-2 print:text-orange-800 print:mb-2 print:text-sm">
                        <CreditCard className="w-5 h-5 print:w-5 print:h-5" />
                        Payment Information
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Amount:</span>
                          <span className="ml-2 text-gray-900 font-bold text-lg print:text-base">₹{bookingData.fare}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Status:</span>
                          <Badge
                            variant={bookingData.paymentStatus ? "default" : "secondary"}
                            className={`ml-2 font-bold ${
                              bookingData.paymentStatus 
                                ? "bg-green-600 print:bg-green-600" 
                                : "bg-orange-600 print:bg-orange-600"
                            }`}
                          >
                            {bookingData.paymentStatus ? "PAID" : "NOT PAID"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - 100×100px Placeholder */}
                  <div className="flex items-center justify-center">
                    <div className="w-[100px] h-[100px] bg-gray-100 border border-gray-300 rounded-lg print:bg-gray-100 print:border-gray-300"></div>
                  </div>
                </div>

                {/* Footer Information */}
                <div className="mt-8 pt-6 border-t border-gray-200 print:mt-5 print:pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-600 print:gap-3 print:text-xs">
                    <div>
                      <span className="font-medium">Booking Type:</span>
                      <span className="ml-1 text-orange-600 font-semibold print:text-orange-600">STUDENT BOOKING</span>
                    </div>
                    <div>
                      <span className="font-medium">Booked On:</span>
                      <span className="ml-1">{format(new Date(), 'PPpp')}</span>
                    </div>
                    <div>
                      <span className="font-medium">Remarks:</span>
                      <span className="ml-1">Valid only with college ID card</span>
                    </div>
                  </div>
                  <div className="mt-4 text-center print:mt-3">
                    <p className="text-xs text-gray-500 print:text-xs">
                      This is a computer-generated ticket. Please carry a valid ID along with this pass.
                    </p>
                    {bookingData.paymentStatus && (
                      <p className="text-xs text-green-600 text-center mt-1 print:text-green-600">
                        ✓ Payment completed successfully
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="print:hidden"
          >
            <Card className="shadow-lg border-orange-200">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={handlePrint}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <Printer className="w-5 h-5 mr-2" />
                    Print Ticket
                  </Button>
                  
                  <Button
                    onClick={handleBookAnother}
                    className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Book Another Ticket
                  </Button>
                  
                  <Button
                    onClick={() => router.push('/')}
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <HomeIcon className="w-4 h-4 mr-2" />
                    Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}