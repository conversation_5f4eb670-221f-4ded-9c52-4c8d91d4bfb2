'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { Ticket, Printer, Plus, Home, CheckCircle, Calendar, Bus, MapPin, User, CreditCard } from 'lucide-react';
import { toast } from 'sonner';

function SpecialPassTicket() {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData, resetSpecialPassData } = useSpecialPass();
  const [currentDate] = useState(new Date());

  // Simple check to ensure we reached this page through proper flow
  useEffect(() => {
    // Only check if payment method was selected - all other validations should have been done before payment
    if (!specialPassData.paymentMethod) {
      // toast.error('Invalid access. Please complete the booking process.');
      router.push('/admin/special-pass');
      return;
    }
  }, [specialPassData.paymentMethod, router]);

  const handleBookAnother = () => {
    resetSpecialPassData();
    router.push('/admin/special-pass');
    toast.info('Starting new special booking');
  };

  const handleBackToDashboard = () => {
    resetSpecialPassData();
    router.push('/admin/dashboard');
  };

  const getPaymentStatusDisplay = () => {
    if (specialPassData.paymentMethod === 'online') {
      return {
        status: 'PAID ONLINE',
        color: 'text-green-700',
        bgColor: 'bg-green-100',
        icon: <CheckCircle className="w-4 h-4" />
      };
    } else {
      return {
        status: 'PAY AT COLLEGE',
        color: 'text-orange-700',
        bgColor: 'bg-orange-100',
        icon: <CreditCard className="w-4 h-4" />
      };
    }
  };

  const paymentDisplay = getPaymentStatusDisplay();

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gradient-to-br from-orange-50 via-white to-red-50 print:min-h-0 print:p-3">
        <div className="max-w-4xl mx-auto print:max-w-none print:mx-2">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8 print:hidden"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <CheckCircle className="w-12 h-12 text-green-600" />
              <h1 className="text-4xl font-bold text-gray-800">Special Booking Confirmed!</h1>
            </div>
            <p className="text-gray-600 text-lg">Your special bus pass has been successfully created</p>
          </motion.div>

          {/* Ticket */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8 print:mb-2"
          >
            <Card className="shadow-2xl border-2 border-orange-300 bg-white print:shadow-md print:border-2 print:border-orange-400">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg print:bg-gradient-to-r print:from-orange-600 print:to-red-600 print:py-4">
                <CardTitle className="text-center">
                  <div className="flex items-center justify-center gap-3 mb-2">
                    <Ticket className="w-8 h-8 print:w-7 print:h-7" />
                    <span className="text-2xl font-bold print:text-xl">ST. JOSEPH'S COLLEGE OF ENGINEERING AND TECHNOLOGY, PALAI</span>
                  </div>
                  <div className="text-lg print:text-base">Special Bus Pass Ticket</div>
                  <div className="text-sm opacity-90 print:text-sm print:opacity-100">Admin Generated</div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8 print:p-5">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 print:gap-5">
                  {/* Left Column - All Information */}
                  <div className="lg:col-span-2 space-y-6 print:space-y-4">
                    {/* Student Information */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 print:bg-blue-50 print:p-3 print:border-blue-200">
                      <h3 className="font-semibold text-blue-800 mb-3 flex items-center gap-2 print:text-blue-800 print:mb-2 print:text-sm">
                        <User className="w-5 h-5 print:w-5 print:h-5" />
                        Student Information
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Name:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{specialPassData.studentName}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Admission No:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{specialPassData.admissionNumber}</span>
                        </div>
                      </div>
                    </div>

                    {/* Travel Dates */}
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 print:bg-green-50 print:p-3 print:border-green-200">
                      <h3 className="font-semibold text-green-800 mb-3 flex items-center gap-2 print:text-green-800 print:mb-2 print:text-sm">
                        <Calendar className="w-5 h-5 print:w-5 print:h-5" />
                        Travel Dates
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Go Date:</span>
                          <span className="ml-2 text-gray-900 font-semibold">
                            {new Date(specialPassData.goDate).toLocaleDateString('en-US', { 
                              weekday: 'short', 
                              year: 'numeric', 
                              month: 'short', 
                              day: 'numeric' 
                            })}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Return Date:</span>
                          <span className="ml-2 text-gray-900 font-semibold">
                            {new Date(specialPassData.returnDate).toLocaleDateString('en-US', { 
                              weekday: 'short', 
                              year: 'numeric', 
                              month: 'short', 
                              day: 'numeric' 
                            })}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Bus Information */}
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 print:bg-purple-50 print:p-3 print:border-purple-200">
                      <h3 className="font-semibold text-purple-800 mb-3 flex items-center gap-2 print:text-purple-800 print:mb-2 print:text-sm">
                        <Bus className="w-5 h-5 print:w-5 print:h-5" />
                        Bus Information
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Bus Name:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{specialPassData.busName}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Route:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{specialPassData.busRoute}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Destination:</span>
                          <span className="ml-2 text-gray-900 font-semibold">{specialPassData.destination}</span>
                        </div>
                      </div>
                    </div>

                    {/* Payment Information */}
                    <div className={`${paymentDisplay.bgColor} border rounded-lg p-4 print:${paymentDisplay.bgColor} print:p-3 print:border`}>
                      <h3 className={`font-semibold ${paymentDisplay.color} mb-3 flex items-center gap-2 print:${paymentDisplay.color} print:mb-2 print:text-sm`}>
                        {paymentDisplay.icon}
                        Payment Information
                      </h3>
                      <div className="space-y-2 text-sm print:space-y-1 print:text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Amount:</span>
                          <span className="ml-2 text-gray-900 font-bold text-lg print:text-base">₹{specialPassData.fare}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Status:</span>
                          <span className={`ml-2 font-bold ${paymentDisplay.color} print:${paymentDisplay.color}`}>
                            {paymentDisplay.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - 100×100px Placeholder */}
                  <div className="flex items-center justify-center">
                    <div className="w-[100px] h-[100px] bg-gray-100 border border-gray-300 rounded-lg print:bg-gray-100 print:border-gray-300"></div>
                  </div>
                </div>

                {/* Footer Information */}
                <div className="mt-8 pt-6 border-t border-gray-200 print:mt-5 print:pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-600 print:gap-3 print:text-xs">
                    <div>
                      <span className="font-medium">Booking Type:</span>
                      <span className="ml-1 text-orange-600 font-semibold print:text-orange-600">SPECIAL ADMIN BOOKING</span>
                    </div>
                    <div>
                      <span className="font-medium">Generated By:</span>
                      <span className="ml-1">{user?.full_name}</span>
                    </div>
                    <div>
                      <span className="font-medium">Generated On:</span>
                      <span className="ml-1">{currentDate.toLocaleDateString()} {currentDate.toLocaleTimeString()}</span>
                    </div>
                  </div>
                  <div className="mt-4 text-center print:mt-3">
                    <p className="text-xs text-gray-500 print:text-xs">
                      This is a computer-generated ticket. Please carry a valid ID along with this pass.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="print:hidden"
          >
            <Card className="shadow-lg border-orange-200">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={() => window.print()}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <Printer className="w-5 h-5 mr-2" />
                    Print Ticket
                  </Button>
                  
                  <Button
                    onClick={handleBookAnother}
                    className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Book Another Special Ticket
                  </Button>
                  
                  <Button
                    onClick={handleBackToDashboard}
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <Home className="w-5 h-5 mr-2" />
                    Back to Admin Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassTicket);
