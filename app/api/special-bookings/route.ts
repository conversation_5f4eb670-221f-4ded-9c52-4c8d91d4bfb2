import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { checkAdminAuth } from '@/lib/auth-utils';

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const adminUser = await checkAdminAuth(request);
    if (!adminUser) {
      return NextResponse.json({ 
        success: false, 
        error: 'Admin authentication required' 
      }, { status: 401 });
    }

    const body = await request.json();
    const {
      studentName,
      admissionNumber,
      busRoute,
      destination,
      paymentStatus,
      goDate,
      returnDate,
      fare,
      busName,
      isSpecialBooking,
      razorpay_payment_id,
      razorpay_order_id,
      razorpay_signature,
    } = body;

    console.log('Creating special booking with data:', {
      studentName,
      admissionNumber,
      busRoute,
      destination,
      paymentStatus,
      goDate,
      returnDate,
      fare,
      busName,
      isSpecialBooking,
      adminUser: adminUser.username,
      hasRazorpayData: !!(razorpay_payment_id || razorpay_order_id || razorpay_signature),
    });

    // Validate required fields
    if (!studentName || !admissionNumber || !busRoute || !destination || !goDate || !returnDate || !fare || !busName) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }

    // Validate student name format (2-3 words)
    const nameParts = studentName.trim().split(/\s+/);
    if (nameParts.length < 2 || nameParts.length > 3) {
      return NextResponse.json({
        success: false,
        error: 'Student name must contain 2-3 words (first name + surname + optional middle name)'
      }, { status: 400 });
    }

    // Validate admission number format
    const admissionRegex = /^[0-9]{2}[A-Z]{2,4}[0-9]{3}$/;
    if (!admissionRegex.test(admissionNumber)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid admission number format. Expected: 2 digits + 2-4 letters + 3 digits'
      }, { status: 400 });
    }

    // Validate dates
    const goDateObj = new Date(goDate);
    const returnDateObj = new Date(returnDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (goDateObj < today) {
      return NextResponse.json({
        success: false,
        error: 'Go date cannot be in the past'
      }, { status: 400 });
    }

    if (returnDateObj <= goDateObj) {
      return NextResponse.json({
        success: false,
        error: 'Return date must be after go date'
      }, { status: 400 });
    }

    // Verify bus exists and is active
    const { data: busData, error: busError } = await supabaseAdmin
      .from('buses')
      .select('id, name, route_code, is_active')
      .eq('route_code', busRoute)
      .eq('is_active', true)
      .single();

    if (busError || !busData) {
      console.error('Bus verification failed:', busError);
      return NextResponse.json({
        success: false,
        error: 'Invalid or inactive bus route'
      }, { status: 400 });
    }

    // Verify route stop exists and is active
    const { data: routeStop, error: stopError } = await supabaseAdmin
      .from('route_stops')
      .select('id, stop_name, fare, is_active')
      .eq('route_code', busRoute)
      .eq('stop_name', destination)
      .eq('is_active', true)
      .single();

    if (stopError || !routeStop) {
      console.error('Route stop verification failed:', stopError);
      return NextResponse.json({
        success: false,
        error: 'Invalid destination for the selected route'
      }, { status: 400 });
    }

    // Verify fare matches
    if (routeStop.fare !== fare) {
      return NextResponse.json({
        success: false,
        error: 'Fare mismatch. Please refresh and try again.'
      }, { status: 400 });
    }

    // Check for duplicate booking (same student, same dates, same route)
    const { data: existingBooking, error: duplicateError } = await supabaseAdmin
      .from('bookings')
      .select('id')
      .eq('admission_number', admissionNumber)
      .eq('go_date', goDate)
      .eq('return_date', returnDate)
      .eq('bus_route', busRoute)
      .single();

    if (existingBooking) {
      return NextResponse.json({
        success: false,
        error: 'A booking already exists for this student with the same dates and route'
      }, { status: 409 });
    }

    // Create the special booking
    const bookingData = {
      admission_number: admissionNumber,
      student_name: studentName,
      bus_route: busRoute,
      destination: destination,
      payment_status: paymentStatus,
      go_date: goDate,
      return_date: returnDate,
      fare: fare,
      bus_name: busName,
      is_special_booking: true, // Flag to identify special bookings (existing)
      is_special: true, // New: also set legacy/current column 'is_special' to true
      created_by_admin: adminUser.username,
      razorpay_payment_id: razorpay_payment_id || null,
      razorpay_order_id: razorpay_order_id || null,
      razorpay_signature: razorpay_signature || null,
      created_at: new Date().toISOString(),
    };

    const { data: booking, error: bookingError } = await supabaseAdmin
      .from('bookings')
      .insert([bookingData])
      .select()
      .single();

    if (bookingError) {
      console.error('Failed to create special booking:', bookingError);
      return NextResponse.json({
        success: false,
        error: 'Failed to create booking. Please try again.'
      }, { status: 500 });
    }

    console.log('Special booking created successfully:', {
      bookingId: booking.id,
      studentName,
      admissionNumber,
      busRoute,
      destination,
      paymentStatus,
      adminUser: adminUser.username
    });

    return NextResponse.json({
      success: true,
      data: {
        booking_id: booking.id,
        message: 'Special booking created successfully'
      }
    });

  } catch (error) {
    console.error('Special booking creation error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error. Please try again.'
    }, { status: 500 });
  }
}

// GET method to retrieve special bookings (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const adminUser = await checkAdminAuth(request);
    if (!adminUser) {
      return NextResponse.json({ 
        success: false, 
        error: 'Admin authentication required' 
      }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get special bookings with pagination
    const { data: bookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select('*')
      .eq('is_special_booking', true)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (bookingsError) {
      console.error('Failed to fetch special bookings:', bookingsError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch special bookings'
      }, { status: 500 });
    }

    // Get total count
    const { count, error: countError } = await supabaseAdmin
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('is_special_booking', true);

    if (countError) {
      console.error('Failed to get special bookings count:', countError);
    }

    return NextResponse.json({
      success: true,
      data: {
        bookings: bookings || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }
    });

  } catch (error) {
    console.error('Special bookings fetch error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
